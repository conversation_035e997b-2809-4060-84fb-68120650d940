enum AppFlavor { admin, student }

class AppConfig {
  static AppFlavor? appFlavor;

  static String get appName {
    switch (appFlavor) {
      case AppFlavor.admin:
        return 'Smart Edu Admin';
      case AppFlavor.student:
        return 'Smart Edu Student';
      default:
        return 'Smart Edu';
    }
  }

  static String get appId {
    switch (appFlavor) {
      case AppFlavor.admin:
        return 'com.smartedu.admin';
      case AppFlavor.student:
        return 'com.smartedu.student';
      default:
        return 'com.smartedu.app';
    }
  }

  static bool get isAdmin => appFlavor == AppFlavor.admin;
  static bool get isStudent => appFlavor == AppFlavor.student;

  static void setFlavor(AppFlavor flavor) {
    appFlavor = flavor;
  }
}
