class Video {
  final String id;
  final String lessonId;
  final String name;
  final String? description;
  final String color;
  final String? thumbnailUrl;
  final String? videoUrl360p;
  final String? videoUrl480p;
  final String? videoUrl720p;
  final String? videoUrl1080p;
  final int orderIndex;
  final int durationSeconds;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Video({
    required this.id,
    required this.lessonId,
    required this.name,
    this.description,
    required this.color,
    this.thumbnailUrl,
    this.videoUrl360p,
    this.videoUrl480p,
    this.videoUrl720p,
    this.videoUrl1080p,
    required this.orderIndex,
    required this.durationSeconds,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Video.fromJson(Map<String, dynamic> json) {
    return Video(
      id: json['id'] as String,
      lessonId: json['lesson_id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      color: json['color'] as String,
      thumbnailUrl: json['thumbnail_url'] as String?,
      videoUrl360p: json['video_url_360p'] as String?,
      videoUrl480p: json['video_url_480p'] as String?,
      videoUrl720p: json['video_url_720p'] as String?,
      videoUrl1080p: json['video_url_1080p'] as String?,
      orderIndex: json['order_index'] as int,
      durationSeconds: json['duration_seconds'] as int? ?? 0,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'lesson_id': lessonId,
      'name': name,
      'description': description,
      'color': color,
      'thumbnail_url': thumbnailUrl,
      'video_url_360p': videoUrl360p,
      'video_url_480p': videoUrl480p,
      'video_url_720p': videoUrl720p,
      'video_url_1080p': videoUrl1080p,
      'order_index': orderIndex,
      'duration_seconds': durationSeconds,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Video copyWith({
    String? id,
    String? lessonId,
    String? name,
    String? description,
    String? color,
    String? thumbnailUrl,
    String? videoUrl360p,
    String? videoUrl480p,
    String? videoUrl720p,
    String? videoUrl1080p,
    int? orderIndex,
    int? durationSeconds,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Video(
      id: id ?? this.id,
      lessonId: lessonId ?? this.lessonId,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      videoUrl360p: videoUrl360p ?? this.videoUrl360p,
      videoUrl480p: videoUrl480p ?? this.videoUrl480p,
      videoUrl720p: videoUrl720p ?? this.videoUrl720p,
      videoUrl1080p: videoUrl1080p ?? this.videoUrl1080p,
      orderIndex: orderIndex ?? this.orderIndex,
      durationSeconds: durationSeconds ?? this.durationSeconds,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get the best available video URL based on quality preference
  String? getBestVideoUrl([List<String> preferredQualities = const ['1080p', '720p', '480p', '360p']]) {
    for (final quality in preferredQualities) {
      switch (quality) {
        case '1080p':
          if (videoUrl1080p != null) return videoUrl1080p;
          break;
        case '720p':
          if (videoUrl720p != null) return videoUrl720p;
          break;
        case '480p':
          if (videoUrl480p != null) return videoUrl480p;
          break;
        case '360p':
          if (videoUrl360p != null) return videoUrl360p;
          break;
      }
    }
    return null;
  }

  /// Get all available video qualities
  List<String> getAvailableQualities() {
    final qualities = <String>[];
    if (videoUrl360p != null) qualities.add('360p');
    if (videoUrl480p != null) qualities.add('480p');
    if (videoUrl720p != null) qualities.add('720p');
    if (videoUrl1080p != null) qualities.add('1080p');
    return qualities;
  }

  /// Format duration as MM:SS or HH:MM:SS
  String get formattedDuration {
    final hours = durationSeconds ~/ 3600;
    final minutes = (durationSeconds % 3600) ~/ 60;
    final seconds = durationSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Video && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Video(id: $id, name: $name, lessonId: $lessonId, duration: $formattedDuration)';
  }
}
