{"version": "2.0.0", "tasks": [{"label": "Supabase: Open Dashboard", "type": "shell", "command": "start", "args": ["https://supabase.com/dashboard/project/ztaocelulisshtkevuzv"]}, {"label": "Supabase: Create Migration", "type": "shell", "command": "supabase", "args": ["migration", "new", "${input:migrationName}"]}, {"label": "Supabase: Push to Database", "type": "shell", "command": "supabase", "args": ["db", "push"]}, {"label": "Flutter: Run Student App", "type": "shell", "command": "flutter", "args": ["run", "-t", "lib/main_student.dart"]}, {"label": "Flutter: <PERSON>", "type": "shell", "command": "flutter", "args": ["run", "-t", "lib/main_admin.dart"]}, {"label": "Flutter: Build Student APK", "type": "shell", "command": "flutter", "args": ["build", "apk", "-t", "lib/main_student.dart", "--release"]}, {"label": "Flutter: Build Admin APK", "type": "shell", "command": "flutter", "args": ["build", "apk", "-t", "lib/main_admin.dart", "--release"]}], "inputs": [{"id": "migrationName", "description": "Migration name", "default": "new_migration", "type": "promptString"}]}