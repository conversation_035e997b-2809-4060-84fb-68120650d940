{"version": "2.0.0", "tasks": [{"label": "Supabase: Open Dashboard", "type": "shell", "command": "start", "args": ["https://supabase.com/dashboard/project/ztaocelulisshtkevuzv"]}, {"label": "Supabase: Create Migration", "type": "shell", "command": "supabase", "args": ["migration", "new", "${input:migrationName}"]}, {"label": "Supabase: Push to Database", "type": "shell", "command": "supabase", "args": ["db", "push"]}, {"label": "Flutter: Run", "type": "shell", "command": "flutter", "args": ["run"]}], "inputs": [{"id": "migrationName", "description": "Migration name", "default": "new_migration", "type": "promptString"}]}