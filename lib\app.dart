import 'package:flutter/material.dart';
import 'config/app_config.dart';
import 'core/theme/app_theme.dart';
import 'features/admin/presentation/pages/admin_home_page.dart';
import 'features/student/presentation/pages/student_home_page.dart';

class SmartEduApp extends StatelessWidget {
  const SmartEduApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConfig.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: AppConfig.isAdmin 
          ? const AdminHomePage() 
          : const StudentHomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}
