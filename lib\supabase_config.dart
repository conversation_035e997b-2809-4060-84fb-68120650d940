import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  static const String url = 'https://ztaocelulisshtkevuzv.supabase.co';
  static const String anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp0YW9jZWx1bGlzc2h0a2V2dXp2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ2MDc2NjcsImV4cCI6MjA3MDE4MzY2N30.aooxR7IXEn_X31a9BW1W5wbjmuoGaa-atWNuXRiq1aM';
  
  static SupabaseClient get client => Supabase.instance.client;
  
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: url,
      anonKey: anon<PERSON>ey,
    );
  }
}
