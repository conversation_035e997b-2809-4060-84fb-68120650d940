class ActivationCode {
  final String id;
  final String code;
  final String? prefix;
  final String? qrCodeData;
  final bool isUsed;
  final String? usedByDeviceId;
  final DateTime? usedAt;
  final DateTime? expiresAt;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ActivationCode({
    required this.id,
    required this.code,
    this.prefix,
    this.qrCodeData,
    required this.isUsed,
    this.usedByDeviceId,
    this.usedAt,
    this.expiresAt,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ActivationCode.fromJson(Map<String, dynamic> json) {
    return ActivationCode(
      id: json['id'] as String,
      code: json['code'] as String,
      prefix: json['prefix'] as String?,
      qrCodeData: json['qr_code_data'] as String?,
      isUsed: json['is_used'] as bool,
      usedByDeviceId: json['used_by_device_id'] as String?,
      usedAt: json['used_at'] != null ? DateTime.parse(json['used_at'] as String) : null,
      expiresAt: json['expires_at'] != null ? DateTime.parse(json['expires_at'] as String) : null,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'prefix': prefix,
      'qr_code_data': qrCodeData,
      'is_used': isUsed,
      'used_by_device_id': usedByDeviceId,
      'used_at': usedAt?.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ActivationCode copyWith({
    String? id,
    String? code,
    String? prefix,
    String? qrCodeData,
    bool? isUsed,
    String? usedByDeviceId,
    DateTime? usedAt,
    DateTime? expiresAt,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ActivationCode(
      id: id ?? this.id,
      code: code ?? this.code,
      prefix: prefix ?? this.prefix,
      qrCodeData: qrCodeData ?? this.qrCodeData,
      isUsed: isUsed ?? this.isUsed,
      usedByDeviceId: usedByDeviceId ?? this.usedByDeviceId,
      usedAt: usedAt ?? this.usedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if the code is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if the code is valid for use
  bool get isValidForUse {
    return isActive && !isUsed && !isExpired;
  }

  /// Get the display code with prefix if available
  String get displayCode {
    if (prefix != null && prefix!.isNotEmpty) {
      return '$prefix-$code';
    }
    return code;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ActivationCode && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ActivationCode(id: $id, code: $displayCode, isUsed: $isUsed, isActive: $isActive)';
  }
}
