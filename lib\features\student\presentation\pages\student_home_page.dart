import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class StudentHomePage extends StatefulWidget {
  const StudentHomePage({super.key});

  @override
  State<StudentHomePage> createState() => _StudentHomePageState();
}

class _StudentHomePageState extends State<StudentHomePage> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Smart Edu'),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Implement sync functionality
            },
            icon: const Icon(Icons.sync),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: IndexedStack(
        index: _currentIndex,
        children: const [
          VideosPage(),
          QuizzesPage(),
          SubscriptionPage(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.video_library),
            label: 'الفيديوهات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.quiz),
            label: 'الاختبارات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.card_membership),
            label: 'تفعيل اشتراك',
          ),
        ],
      ),
    );
  }
}

// Videos Page
class VideosPage extends StatelessWidget {
  const VideosPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header with trial button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الأقسام المدفوعة',
                style: AppTextStyles.h5.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Navigate to trial sections
                },
                icon: const Icon(Icons.play_circle_outline),
                label: const Text('تجربة التطبيق'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.accentGreen,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Paid Sections Grid
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.2,
              ),
              itemCount: 6, // TODO: Replace with actual data
              itemBuilder: (context, index) {
                return _buildSectionCard(
                  context,
                  title: 'البكالوريا العلمي',
                  subtitle: '12 مادة',
                  color: AppColors.primaryBlue,
                  isLocked: true,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required Color color,
    required bool isLocked,
  }) {
    return Card(
      child: InkWell(
        onTap: () {
          // TODO: Navigate to section details
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  isLocked ? Icons.lock : Icons.school,
                  size: 24,
                  color: color,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Quizzes Page
class QuizzesPage extends StatelessWidget {
  const QuizzesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'صفحة الاختبارات\n(سيتم تطويرها لاحقاً)',
        textAlign: TextAlign.center,
        style: AppTextStyles.h5,
      ),
    );
  }
}

// Subscription Page
class SubscriptionPage extends StatelessWidget {
  const SubscriptionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                children: [
                  Icon(
                    Icons.card_membership,
                    size: 48,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'تفعيل الاشتراك',
                    style: AppTextStyles.h4.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'أدخل كود التفعيل للوصول للمحتوى المدفوع',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Code Input
          TextField(
            decoration: const InputDecoration(
              labelText: 'كود التفعيل',
              hintText: 'أدخل كود التفعيل هنا',
              prefixIcon: Icon(Icons.vpn_key),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          
          // Activate Button
          ElevatedButton.icon(
            onPressed: () {
              // TODO: Implement activation
            },
            icon: const Icon(Icons.check_circle),
            label: const Text('تفعيل الاشتراك'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
          const SizedBox(height: 16),
          
          // QR Code Scanner Button
          OutlinedButton.icon(
            onPressed: () {
              // TODO: Implement QR scanner
            },
            icon: const Icon(Icons.qr_code_scanner),
            label: const Text('مسح الباركود'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ],
      ),
    );
  }
}
